import sys
import re
import json
from scipy.spatial import distance as sp_distance
import math
from fuzzywuzzy import fuzz
import spacy
from spacy.matcher import Matcher
import pandas as pd 
from fuzzywuzzy import fuzz
from Invoice.ML_Vendor_Extraction import *
import json
import pandas as pd  
#import lexnlp.extract.en.entities.nltk_re
from PIL import Image
#import Image
#import lexnlp.extract.en.urls
from urllib.parse import urlparse
import traceback
import tldextract
from Invoice.DbConfig import *
from spacy.matcher import PhraseMatcher,Matcher
from fuzzywuzzy import process

#extract object initiated 
#extract = tldextract.TLDExtratct()



def getTextFromBox(json_path,box,im_width,im_height):

    f = open(json_path,) 
    data = json.load(f)
    
    x1= box[0] -50
    x2= box[1] + 100
    y1= box[2] 
    y2= box[3]
    print("get text from box ",y1,x1,y2,x2)
    text=[]
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":

            BoundingBox=d["Geometry"]["BoundingBox"]
            add=d["Geometry"]["Polygon"]

             
            y1_= (add[0]["Y"]*im_height)
            y2_= (add[3]["Y"]*im_height)
            x1_= (add[0]["X"]*im_width)
            x2_= (add[2]["X"]*im_width)
            
            #print(d["Text"],"--",y1_,x1_,y2_,x2_)
            
            
            if y1_>y1 and y2_<y2 and x1_>x1 and x2_<x2:
                print("ex text ",d["Text"])
                if len(d["Text"])>=3:
                    text.append(d["Text"])
    print("text from box ",text)
    return text 



def getVendorByTop(json_path,im_height,vendorlist,config_name,general_entity_fields):

    f = open(json_path,) 
    data = json.load(f)

    lines=[]
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":
            top=d["Geometry"]["BoundingBox"]["Top"]
            lines.append( (d["Text"],top) )
            if top>(im_height*30):
                break
    
    f=[]
    for l in lines:
        #r=list(lexnlp.extract.en.entities.nltk_re.get_companies(l[0]))
        r=spacy_getorg(l[0],config_name,general_entity_fields)
        
        #if vendor list is from blocked content skip this vendor name
        if getfuzz_score_list(vendorlist,l[0]) is not None:
            continue
                        

        if len(r)>0:
            f.append(l)
            #print(l)
    print("all mathched results for vendors ",f)
    if len(f)>0:        
        f.sort(key = lambda x: x[1])
        return f[0]
    else:
        return None

def spacy_getorg(text,config_name,gen_fields):
    res=[]
    entity=[]
   

    #gen_fields=get_General_entity(config_name)
    #print("gen fields ",gen_fields)

    pharase_matcher = PhraseMatcher(nlp.vocab)

    for item in gen_fields:
        #print("item ",item)
        if item['fieldtype']=="ORG":
            #entity_str.append( (item['str'],item['entity'] ) )
            entity.append(item['entity'])
    #print("entity ",entity)
    if len(entity)>0:
        patterns = [nlp(e) for e in entity]
        pharase_matcher.add("ENTITY_PATTERN", patterns)
    

    
    doc=nlp(text)
    matches2 = pharase_matcher(doc)
    #print("vendor phrase matcher  2 ",matches2)        

    for e in doc.ents:
        if e.label_=="ORG":
            res.append(e.text)
    
    #if len(res)==0:
    for match_id, start, end in matches2:
        span = doc[start:end]
        match_res=span.text
        res.append(match_res)
        
    return res




def spacy_geturl(text):

    urls=[]
    doc=nlp(text)
    for e in doc:
        if e.like_url:
            urls.append(e)
    
    return urls

  
def getVendorbyMatch(boxtext,company_list,config_name,general_entity_fields):
    
    #print("general entities getvendorbymatch ",general_entity_fields)
    f=[]
    #s = ' '.join([str(n) for n in boxtext])
    #boxtext=[s]

    for l in boxtext:
        #r=list(lexnlp.extract.en.entities.nltk_re.get_companies(l))
        r=spacy_getorg(l,config_name,general_entity_fields)
        print("vendor ",r)
        if len(r)>0:
            #exclusion rule for removing block lists from vendor names
            fuzz_result=getfuzz_score_list(company_list,r[0])
            if fuzz_result is not None:
                continue
            f.append(r[0])
            print("ven ext spacy ",r)
    print("results of f ",f)
    if len(f)>0:
        
        #f.sort(key = lambda x: x[1])
        return f[0]
    else:
        return None

def get_fuzz_score(str1, str2):

    from fuzzywuzzy import fuzz
    partial_ratio = fuzz.partial_ratio(str1.lower(), str2.lower())
    return partial_ratio

def getfuzz_score_list(arr,str1):
    
    final_match=None
    for v in arr :
        ratio = fuzz.ratio(v.lower(), str1.lower())
        #print("vendor match fuzz ",v.lower(), str1.lower(),ratio)
        if ratio>85:
            final_match=v
    
    return final_match




def getVendorByMail(json_path,vendorlist):
    
    email_list=[]
    url_list=[]

    with open(json_path) as json_file:
        data=json.load(json_file)
    
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":
            #print(d["Text"])
            text= d["Text"]
            emails = re.findall(r"[a-z0-9\.\-+_]+@[a-z0-9\.\-+_]+\.[a-z]+", text)
            #url=list(lexnlp.extract.en.urls.get_urls(text))
            url=spacy_geturl(text)
            #print(url )
            if len(url)>0:
                #parsed_uri = urlparse(str(url[0]))

                #if parsed_uri.netloc!="":
                #    domain=parsed_uri.netloc.split(".")[0]
                #else:
                #    domain=parsed_uri.path.split(".")[0]
                res=tldextract.extract(str(url[0]) )
                domain=str(res.domain)
                #domain=parsed_uri.netloc.split(".")[0]
                if domain is not None:
                    if getfuzz_score_list(vendorlist,domain) is None:
                        url_list.append(domain)
            if len(emails)>0:
                print("emails found ",emails,getfuzz_score_list(vendorlist,emails[0]))

                if getfuzz_score_list(vendorlist,emails[0]) is None:
                    email_list.append(emails[0].split("@")[1].split(".")[0])
    
    print("url list ",url_list)
    print("email list ",email_list)
    url_list=[x for x in url_list if x]
    email_list=[x for x in email_list if x]

    if len(url_list)>0:
        
        return url_list[0]
    
    if len(email_list) >0:
        return email_list[0]

    return None

def findfullyqualifiedcompanyname(json_path,vendorname,vendorlist,general_entity_fields,config_name):
    text_data=[]
    with open(json_path) as json_file:

        data=json.load(json_file)

        for blocks in data['Blocks']:
            if (blocks["BlockType"]=='LINE'):
                text_data.append(blocks["Text"])
    
    company_list=[]
    for i in text_data:
        #r=list(lexnlp.extract.en.entities.nltk_re.get_companies(i))
        r=spacy_getorg(i,config_name,general_entity_fields)

        if len(r)>0 :
            if getfuzz_score_list(vendorlist,r[0]) is None:
                company_list.append(r[0])
    res=[]
    for i in company_list:
        score=get_fuzz_score(i, vendorname)
        if score>90:
            res.append((i,score))
    
    res.sort(key = lambda x: x[1],reverse=True)
    if len(res)>0:
        return res[0][0]
    else:
        return vendorname
    


        
    



       

def getVendorByLogoMatch(json_path,boxtext,vendorlist,config_name,general_entity_fields):
    
    print("IN LOGO MATCH ")
    print("config_name ",config_name)
    print("vendor_list ",vendor_list)
    #s = ' '.join([str(n) for n in boxtext])
    #boxtext=[s]
    print("boxtext ",boxtext)
    #Retrive all the lines in the scanned doc 
    text_data=[]
    with open(json_path) as json_file:

        data=json.load(json_file)

        for blocks in data['Blocks']:
            if (blocks["BlockType"]=='LINE'):
                text_data.append(blocks["Text"])

    ############Extract Company Names ########
    company_list=[]
    #company_list=spacy_getorg(text_data)
    print("text data >>>>>>>>>")
    #print(text_data)
    #print("res ",spacy_getorg("Lighthouse Software",config_name))
    print("length of text data ",len(text_data))

    for i in text_data:
        #r=list(lexnlp.extract.en.entities.nltk_re.get_companies(i))
        r=spacy_getorg(i,config_name,general_entity_fields)

        if len(r)>0 :
            if getfuzz_score_list(vendorlist,r[0]) is None:
                company_list.append(r[0])
    
    result=[]

    print("extracted company list from all file")
    print(company_list)
      
    score=0 
    for i in company_list:
        ven_res=None
        max=0
        for j in boxtext:
            if len(j)>=3:
                score=fuzz.partial_ratio(i.lower(), j.lower())
            #print("matching ",i,j,score)
            if score >= 85:
                if score>max:
                    max=score
                    ven_res=i
               
                #if len(i[1])>70:
                #    ven=j
                #else:
                #    ven=i[1]
               
                result.append([ven_res,score])
                #print(i[1],j,score)
    print("results from match ",result)
    return result

global_config=None
#global_vendorlist=[]
def getVendors(model,image,json_path,companylist,config_name,response_layer,images_list):
    
    #fetching the general entities once from database
    general_entity_fields=get_General_entity(config_name)

    global_config=config_name
    #vendor_list=["td williamson","welldyne"]
    print("companylist ...... ",companylist)
    vendor_list=[]
    if companylist is not None:
        vendor_list=companylist
        #global_vendorlist=company_list
    
    print("vendor list ... ",vendor_list) 

    im = Image.open(image)
    im_width, im_height = im.size
    print("image dim ",im_height,im_width)
    #call ML Model to get the Results 
    p_res=None
    boxtext=[]
    try: 
        p_res=predict(model,image,im_width,im_height)
        print("box value extracted ",p_res)
        
    except Exception:
        
        print("Error While ML Vendor Extraction")  
        print(traceback.format_exc()) 


    if p_res is None:
        if not response_layer.executed:
                response_layer.execute(images_list)
        if response_layer.vendor_name is not None and len(response_layer.vendor_name)>0:
            print("vendor name API 2 ", response_layer.vendor_name)
            #p_res= (response_layer.vendor_name,88)
            match_item=getVendorbyMatch([response_layer.vendor_name],companylist,config_name,general_entity_fields)
            print("get vendr by match API2 ",match_item)
            result=getVendorByLogoMatch(json_path,[response_layer.vendor_name],vendor_list,config_name,general_entity_fields)
            result.sort(key = lambda x: x[1],reverse=True)
        
            #sort results by secound column 
            print("vendor results are ")
            print(result)
            if len(result)>0:
                return result[0][0],result[0][1]
            else:
                #return match_item,confidence
                if match_item is not None:
                    name=findfullyqualifiedcompanyname(json_path,match_item,vendor_list,general_entity_fields,config_name)
                   
                    return name,83
                else:
                    #name=response_layer.vendor_name
                    
                    name=findfullyqualifiedcompanyname(json_path,response_layer.vendor_name,vendor_list,general_entity_fields,config_name)
                    fuzz_result=getfuzz_score_list(companylist,name)
                    #if match is found it means vendname is present in block list 
                    if fuzz_result is None:
                        return name,89
                        
                        
                    


    if p_res is not None:
    
        box=p_res[0]
        confidence=p_res[1]
    
        #extract all the text from bounding box 
        boxtext = getTextFromBox(json_path,box,im_width,im_height)
    
        print("boxtext")
        print(boxtext)
        #check if any text matches the company format 
        match_item=None
        if len(boxtext)>0:
            match_item=getVendorbyMatch(boxtext,companylist,config_name,general_entity_fields)
            print("get vendr by match ",match_item)
    
        #if match_item is not None:
        #    return match_item,confidence
    
        #Search for Match in whole of the Invoice except the Company Name
    
        result=getVendorByLogoMatch(json_path,boxtext,vendor_list,config_name,general_entity_fields)
        result.sort(key = lambda x: x[1],reverse=True)
    
        #sort results by secound column 
        print("vendor results are ")
        print(result)
        if len(result)>0:
            return result[0][0],result[0][1]
        else:
            if match_item is not None:
                name=findfullyqualifiedcompanyname(json_path,match_item,vendor_list,general_entity_fields,config_name)
                    
                return name,confidence
            else:
                if len(boxtext)>0: 
                    #if len(boxtext)>2:
                    #    boxtext=boxtext[0:1]
                    #s = ' '.join([str(n) for n in boxtext])
                    print("boxtext ",boxtext[0])
                    fuzz_result=getfuzz_score_list(vendor_list,boxtext[0])
                    print("fuzz result",fuzz_result)
                    if fuzz_result is None:
                        name=findfullyqualifiedcompanyname(json_path,boxtext[0],vendor_list,general_entity_fields,config_name)
                    
                        return name,66
                        
                    

    
    #Search vendor by email id or url :
    resbymail=getVendorByMail(json_path,vendor_list)
    print("vendor results by email ",resbymail)
    if resbymail is not None:
        result=getVendorByLogoMatch(json_path,[resbymail],vendor_list,config_name,general_entity_fields)
        result.sort(key = lambda x: x[1],reverse=True)
        if len(result)>0:
            return result[0][0],result[0][1]
        else:
            return resbymail,100
    
    if len(boxtext)>0 and getfuzz_score_list(vendor_list,boxtext[0]) is None:
        return boxtext[0],confidence
    else:
        match_item=getVendorByTop(json_path,im_height,vendor_list,config_name,general_entity_fields)
        if match_item is not None:
            return match_item[0],100
    return None,0     
    
    

    
    
    
    
    




















       

    









        

        


