.sticky {
    position: sticky;
    top: 0;
    max-height: 100vh;
}

.toggle-nav-sidebar {
    z-index: 20;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 0 0 23px;
    width: 23px;
    border-right: 1px solid #eaeaea;
    background-color: #ffffff;
    cursor: pointer;
    font-size: 20px;
    color: #447e9b;
    padding: 0;
}

[dir="rtl"] .toggle-nav-sidebar {
    border-left: 1px solid #eaeaea;
    border-right: 0;
}

.toggle-nav-sidebar:hover,
.toggle-nav-sidebar:focus {
    background-color: #f6f6f6;
}

#nav-sidebar {
    z-index: 15;
    flex: 0 0 275px;
    left: -276px;
    margin-left: -276px;
    border-top: 1px solid transparent;
    border-right: 1px solid #eaeaea;
    background-color: #ffffff;
    overflow: auto;
}

[dir="rtl"] #nav-sidebar {
    border-left: 1px solid #eaeaea;
    border-right: 0;
    left: 0;
    margin-left: 0;
    right: -276px;
    margin-right: -276px;
}

.toggle-nav-sidebar::before {
    content: '\00BB';
}

.main.shifted .toggle-nav-sidebar::before {
    content: '\00AB';
}

.main.shifted > #nav-sidebar {
    left: 24px;
    margin-left: 0;
}

[dir="rtl"] .main.shifted > #nav-sidebar {
    left: 0;
    right: 24px;
    margin-right: 0;
}

#nav-sidebar .module th {
    width: 100%;
    overflow-wrap: anywhere;
}

#nav-sidebar .module th,
#nav-sidebar .module caption {
    padding-left: 16px;
}

#nav-sidebar .module td {
    white-space: nowrap;
}

[dir="rtl"] #nav-sidebar .module th,
[dir="rtl"] #nav-sidebar .module caption {
    padding-left: 8px;
    padding-right: 16px;
}

#nav-sidebar .current-app .section:link,
#nav-sidebar .current-app .section:visited {
    color: #ffc;
    font-weight: bold;
}

#nav-sidebar .current-model {
    background: #ffc;
}

@media (max-width: 767px) {
    #nav-sidebar, #toggle-nav-sidebar {
        display: none;
    }
}

.change-list .main > #nav-sidebar+.content {
    overflow: hidden;
}
