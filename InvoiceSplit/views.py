from django.shortcuts import render
from django.shortcuts import render
from rest_framework.views import APIView
from django.shortcuts import render
from django.views.generic import View
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
import json
from django.http import HttpResponse
import fitz
from AMS.settings import BASE_DIR
import uuid
import os
import base64
import pdfplumber
import subprocess
import os.path
from os import path
import time
from AMS.extract_settings import *
from AMS.Invoice_identification_Settings import *
from check_invoice.AWSExtractUtil import *
# importing libraries for pdf to invoice
from pdf2image import convert_from_path, convert_from_bytes
from pdf2image.exceptions import (
    PDFInfoNotInstalledError,
    PDFPageCountError,
    PDFSyntaxError
)
from pikepdf import Pdf
import webbrowser
import json
import boto3
import io
from io import BytesIO
import sys
import base64

from Invoice.DbConfig import *
from Invoice.FieldMatch import *
from Invoice.ML.TFPredictions import *
from Invoice.Forms_Extraction import *
from fuzzywuzzy import fuzz
from Invoice.FormFieldExtractionWrapper import *

import base64
import json
import shutil
import os.path
from os import path
from InvoiceSplit.extractvals import *
import trp.trp2 as t2
from InvoiceSplit.DbConfig import *
import traceback
from .utils import *

# Create your views here.

# Class Serves as View


def reader(file):
    with open(file, "rb") as image_file:
        img_test = image_file.read()
        bytes_test = bytearray(img_test)
    return bytes_test


class InvoiceSplit(APIView):

    def post(self, request):
        del_files = []
        json_body = json.loads(request.body.decode("utf-8"))
        base64__ = json_body["data"]
        invoice_type = json_body["type"]
        mode = json_body["mode"]
        base_path = BASE_DIR+'/extract_check/'
        invoice_path = None

        if "config_name" in json_body:
            config_name = json_body["config_name"]
        else:
            config_name = "global_config"

        if "return_type" in json_body:
            return_type = json_body["return_type"]
        else:
            return_type = "base64"

        # if config name does not exists then
        if not checkconfignameexists(config_name.split('|')[0]):
            config_name = "global_config"
        
        request_id = log_to_db(request, 'InvoiceSplit', 'invoice_split', config_name)
        config_name = config_name.split('|')[0].strip()

        unique_filename = None
        if mode == "test":
            if "extraction_id" in json_body:
                extraction_id = json_body["extraction_id"]
                unique_filename = extraction_id
            else:
                unique_filename = str(uuid.uuid4())

        else:
            unique_filename = str(uuid.uuid4())
        
        if invoice_type == "pdf":
            invoice_path = base_path+unique_filename+".pdf"
        else:
            # invoice is not a pdf
            error = {}
            error["error"] = "Not a pdf file"
            # print("Error ",str(e))
            update_status(request_id, 500, "Not a pdf file")
            return HttpResponse(json.dumps(str(error), indent=4, sort_keys=True, default=str), status=500,)

        with open(os.path.expanduser(invoice_path), 'wb') as fout:
            fout.write(base64.b64decode(base64__))

        print("File Written "+invoice_path)
        fout.close()
        del_files.append(invoice_path)

        images_list = {}
        images_list_700 = {}
        json_files_test = []
        images_count = 0
        # convert the pdf into image
        if invoice_type == "pdf":
            # images = convert_from_path(invoice_path, size=(2000, None))
            images = convert_from_path(invoice_path, size=(2000, None))
            images_700 = convert_from_path(invoice_path, size=(772, 995))
            # for im in images:
            # im.save(base_path+unique_filename+".png")

            for i in images:
                i.save(base_path+unique_filename+"_"+str(images_count)+".png")
                images_list[images_count] = base_path + \
                    unique_filename+"_"+str(images_count)+".png"
                json_files_test.append(
                    base_path+unique_filename+"_"+str(images_count)+".json")
                del_files.append(base_path+unique_filename +
                                 "_"+str(images_count)+".png")
                del_files.append(base_path+unique_filename +
                                 "_"+str(images_count)+".json")
                images_count = images_count+1

            for id, i in enumerate(images_700):
                i.save(base_path+unique_filename+"_"+str(id)+"_700"+".png")
                images_list_700[id] = base_path + \
                    unique_filename+"_"+str(id)+"_700"+".png"
                del_files.append(base_path+unique_filename +
                                 "_"+str(id)+"_700"+".png")

        # finding total length of images
        max_page_scan = get_maxpagescan()

        update_page_count(request_id, len(images_list))

        print("images_list ", images_list)
        print("images list 700 ", images_list_700)
        # return HttpResponse(json.dumps({"result":"ok"},indent=4, sort_keys=True, default=str),status=500)
        """ 
        if images_count>max_page_scan:
            error={"error":"PDF Length greater than allowed Configuration"}
            return HttpResponse(json.dumps(error,indent=4, sort_keys=True, default=str),status=500)
        
        """

        ################### Textract Object #####################

        textract = boto3.client('textract', 'us-east-1')

        # Instance of boto3 client

        client = boto3.client(
            service_name='textract',
            region_name='us-east-1',
            endpoint_url='https://textract.us-east-1.amazonaws.com',aws_access_key_id="********************",
                        aws_secret_access_key="6629LxPd+So34S4EJPxsxs0KOvCwt/AwrjZkAHwN"
        )

        ################### Textract Object ####################

        field_match = FieldMatch()
        print("config_name ", config_name)
        db_extrtaction_list = get_extraction_list(config_name, None)
        print("db extraction list")
        print(db_extrtaction_list)
        w_list = field_match.cerate_word_list(db_extrtaction_list)
        print("word list")
        print(w_list)
        # match_threshold=get_ai_fieldmatch_threshold(config_name)
        match_threshold = 80

        json_files = []

        if mode == "test":
            json_files = json_files_test
        Invoice_obj_list = {}

        counter = 0
        for key in images_list:

            if counter > max_page_scan:
                break

            ikey = key
            inv_info = {}
            IsInvoice = True
            inv_page_path = images_list[key]
            inv_page_path_700 = images_list_700[key]

            I_res_1 = predictInvoice(TFmodel2, inv_page_path)
            print(I_res_1)
            inv_info["isinvoice"] = I_res_1["I"]
            inv_info["path"] = inv_page_path
            if I_res_1["I"] >= 0.45:
                json_path = inv_page_path.replace("png", "json")

                with open(inv_page_path_700, "rb") as image_file:
                    base64_encoded_string = base64.b64encode(image_file.read())

                    data_str = reader(inv_page_path_700)
                    feature_field = ['FORMS']
                    #################### PERFORMING OCR ##################
                    response = None
                    if mode == "notest":
                        print("fetching Boto Response .....")
                        # response = client.analyze_document(Document={'Bytes': data_str}, FeatureTypes=feature_field)

                        with open(inv_page_path_700, "rb") as document:
                            response = textract.analyze_expense(
                                Document={
                                    'Bytes': document.read(),
                                })

                        with open(json_path, 'w') as fp:
                            json.dump(response, fp)
                        json_files.append(json_path)

                    inv_info["json_path"] = json_path
                    # Extract key value pairs
                    # raw_dict = get_raw_values(json_path)
                    print("key ", key)
                    # raw_dict=get_raw_values(json_files[key])
                    # raw_dict=get_raw_values(json_path)
                    raw_dict = getkv(response)

                    field_list = {}
                    # converting k,v to nearest labels
                    for key, value in raw_dict.items():
                        check_res = field_match.get_field_label(
                            key, w_list, match_threshold)
                        key = key.replace(":", "").replace(",", "")

                        if check_res[1] == None:
                            data = {key.strip(): value[0].strip(
                            ), "confidence_level": value[1]}
                            field_list[key.strip()] = value[0].strip()

                        else:
                            data = {check_res[1].strip(): value[0].strip(
                            ), "confidence_level": value[1]}
                            field_list[check_res[1].strip()] = value[0].strip()

                    # field_list.append(data)
                    print("field list ")
                    print(field_list)
                    print("#################")
                    response2 = None

                    if not "Invoice_No" in field_list:
                        # searching invoice number

                        with open(inv_page_path_700, 'rb') as document:
                            imageBytes = bytearray(document.read())

                        # Call Textract AnalyzeDocument by passing a document from local disk
                        response2 = textract.analyze_document(
                            Document={'Bytes': imageBytes},
                            FeatureTypes=["QUERIES"],
                            QueriesConfig={
                                "Queries": [{
                                    "Text": "What is invoice number?",
                                    "Alias": "Invoice_No"
                                }]
                            })

                        d = t2.TDocumentSchema().load(response2)
                        page = d.pages[0]

                        # get_query_answers returns a list of [query, alias, answer]
                        query_answers = d.get_query_answers(page=page)
                        print("query answers ", query_answers,
                              "page path ", inv_page_path_700)
                        if len(query_answers) > 0:
                            if len(query_answers[0][2].strip()) > 0:
                                field_list["Invoice_No"] = query_answers[0][2]
                                print("invoice number not found ,by query",
                                      query_answers[0][2])

                    # print(field_list)
                    # fetching form fields from
                    form_fields = []
                    table_dimensions = [0, 0]

                    # form_fields=getFormFieldsbyDistance(config_name,form_fields,json_path,table_dimensions)
                    # print("form fields :",form_fields)

                    # t1=Invoice_obj_list[0]['form_fields']
                    # t2=Invoice_obj_list[0]['fields']

                    for i in form_fields:
                        key = list(i.keys())[0]
                        if not (key in field_list):
                            field_list[key] = i[key]

                    inv_info["fields"] = field_list
                counter = counter+1
            else:
                IsInvoice = False

            Invoice_obj_list[ikey] = inv_info

        # preparing images clusters
        # keywords to compare
        fields = ["Invoice_No"]

        cluster_list = []
        cluster = []
        for key, value in Invoice_obj_list.items():
            print("*****************")
            print(key, value["isinvoice"], Invoice_obj_list[key])

            if key > 0:
                I1 = None
                I2 = None
                if "fields" in Invoice_obj_list[key-1]:
                    I1 = Invoice_obj_list[key-1]
                elif key-2 in Invoice_obj_list:
                    I1 = Invoice_obj_list[key-2]
                I2 = Invoice_obj_list[key]

                if I1 is None:
                    cluster_list.append(cluster)
                    cluster = []
                    cluster.append(key)

                elif value["isinvoice"] < 0.45:  # i.e its not an invoice
                    cluster.append(key)
                else:  # i.e document is invoice:
                    match_score = 0
                    for i in fields:
                        match = 0
                        absent_field = 0
                        try:
                            if (i in I1['fields']) and (i in I2['fields']):
                                match = fuzz.ratio(I1['fields'][i].replace(
                                    'O', '0').strip(), I2['fields'][i].replace('O', '0').strip())
                                print("in match.... ", match, " -- ",
                                      I1['fields'][i], "-", I2['fields'][i])

                            elif not (i in I2['fields']):
                                print("field is absent")
                                print(key, i)
                                print(I1['fields'])
                                print("******i2")
                                print(I2['fields'])

                                # i.e field is not present in secound Invoice
                                absent_field = absent_field+1
                        except:
                            print(I2)
                            print("----------------------")
                            print(I1)
                            print("exception...")
                            traceback.print_exc()
                            pass

                        print("match is ", match)
                        match_score = match_score+match

                    print("overall match score ", (match_score/len(fields)))
                    if (match_score/len(fields)) > 95:
                        # invoices are same
                        cluster.append(key)
                    elif absent_field == len(fields):
                        cluster.append(key)
                    else:
                        print("in last else...")
                        cluster_list.append(cluster)
                        cluster = []
                        cluster.append(key)
            else:
                cluster.append(key)

        if len(cluster) > 0:
            cluster_list.append(cluster)

        cl = []
        if (invoice_type == "pdf" and return_type == "pdf"):
            for ind, i in enumerate(cluster_list):
                d = {"invoiceid": ind, "invoicedata": self.getBase64OfPDF(
                    invoice_path, i), "pages": {"start": i[0]+1, "end": i[-1]+1}}
                cl.append(d)
        else:
            for i in cluster_list:
                l = []
                for index, val in enumerate(i):
                    # print(index,images_list[val])
                    if return_type == "str":
                        d = {"invoiceid": index, "invoicedata": images_list[val], "pages": {
                            "start": i[0]+1, "end": i[-1]+1}}
                    else:
                        with open(images_list[val], "rb") as image_file:
                            encoded_string = base64.b64encode(
                                image_file.read())
                            encoded_string = encoded_string.decode('utf-8')

                        d = {"invoiceid": index, "invoicedata": encoded_string,
                             "pages": {"start": i[0]+1, "end": i[-1]+1}}

                    l.append(d)
                cl.append(l)

        result = {"extraction_id": unique_filename, "invoices": cl}
        # json.dumps(cl)

        if not mode == "test":  # "extraction_id" not in json_body.keys() :

            # Remove folder list

            for item in del_files:
                if path.exists(item):
                    os.remove(item)
            print("files cleaned...")

        # res = {"result": "done"}
        update_status(request_id, 200)
        return HttpResponse(json.dumps(result, indent=4, sort_keys=True, default=str))

    def get(self, request):

        res = {"response": "ok"}
        return HttpResponse(res)

    def get_base64(self, filepath):
        with open(filepath, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def getBase64OfPDF(self, input_pdf, pages):
        temp_pdf_filepath = input_pdf.replace(".pdf", "")+"_splited.pdf"
        input_pdf = Pdf.open(input_pdf)

        output = Pdf.new()
        for page in pages:
            # output.addPage(input_pdf.getPage(page))
            output.pages.append(input_pdf.pages[page])
        output.save(temp_pdf_filepath)

        # with open(temp_pdf_filepath, "wb") as output_stream:
        # output.write(output_stream)
        base64 = self.get_base64(temp_pdf_filepath)
        if os.path.exists(temp_pdf_filepath):
            os.remove(temp_pdf_filepath)

        return base64


class MultipleInvoice(APIView):

    def post(self, request):
        del_files = []
        json_body = json.loads(request.body.decode("utf-8"))
        base64__ = json_body["data"]
        invoice_type = json_body["type"]
        mode = json_body["mode"]
        base_path = BASE_DIR+'/extract_check/'
        invoice_path = None

        if "config_name" in json_body:
            config_name = json_body["config_name"]
        else:
            config_name = "global_config"

        if "return_type" in json_body:
            return_type = json_body["return_type"]
        else:
            return_type = "base64"

        # if config name does not exists then
        if not checkconfignameexists(config_name.split('|')[0]):
            config_name = "global_config"
        
        request_id = log_to_db(request, 'InvoiceSplit', 'multiple_invoice', config_name)
        config_name = config_name.split('|')[0].strip()

        unique_filename = None
        if mode == "test":
            extraction_id = json_body["extraction_id"]
            unique_filename = extraction_id
        else:
            unique_filename = str(uuid.uuid4())
        
        if invoice_type == "pdf":
            invoice_path = base_path+unique_filename+".pdf"
        else:
            # invoice is not a pdf
            update_status(request_id, 500, 'Not a pdf file')
            error = {}
            error["error"] = "Not a pdf file"
            # print("Error ",str(e))
            return HttpResponse(json.dumps(str(error), indent=4, sort_keys=True, default=str), status=500,)

        with open(os.path.expanduser(invoice_path), 'wb') as fout:
            fout.write(base64.b64decode(base64__))

        print("File Written "+invoice_path)
        fout.close()
        del_files.append(invoice_path)

        images_list = {}
        images_list_700 = {}
        json_files_test = []
        images_count = 0
        # convert the pdf into image
        if invoice_type == "pdf":
            # images = convert_from_path(invoice_path, size=(2000, None))
            images = convert_from_path(invoice_path)
            images_700 = convert_from_path(invoice_path, size=(772, 995))
            # for im in images:
            # im.save(base_path+unique_filename+".png")

            for i in images:
                i.save(base_path+unique_filename+"_"+str(images_count)+".png")
                images_list[images_count] = base_path + \
                    unique_filename+"_"+str(images_count)+".png"
                json_files_test.append(
                    base_path+unique_filename+"_"+str(images_count)+".json")
                del_files.append(base_path+unique_filename +
                                 "_"+str(images_count)+".png")
                del_files.append(base_path+unique_filename +
                                 "_"+str(images_count)+".json")
                images_count = images_count+1

            for id, i in enumerate(images_700):
                i.save(base_path+unique_filename+"_"+str(id)+"_700"+".png")
                images_list_700[id] = base_path + \
                    unique_filename+"_"+str(id)+"_700"+".png"
                del_files.append(base_path+unique_filename +
                                 "_"+str(id)+"_700"+".png")

        cluster_list = []
        cluster = []
        if (len(images_list) > 1):

            ################### Textract Object #####################

            textract = boto3.client('textract', 'us-east-1')

            # Instance of boto3 client
            client = boto3.client(
                service_name='textract',
                region_name='us-east-1',
                endpoint_url='https://textract.us-east-1.amazonaws.com',aws_access_key_id="********************",
                        aws_secret_access_key="6629LxPd+So34S4EJPxsxs0KOvCwt/AwrjZkAHwN"
            )

            ################### Textract Object ####################

            field_match = FieldMatch()
            db_extrtaction_list = get_extraction_list(config_name, None)
            w_list = field_match.cerate_word_list(db_extrtaction_list)

            match_threshold = get_ai_fieldmatch_threshold(config_name)

            json_files = []

            if mode == "test":
                json_files = json_files_test
            Invoice_obj_list = {}

            fields = ["Invoice_No"]
            update_page_count(request_id, len(images_list))
            for key in images_list:
                if (len(cluster_list) <= 1):
                    ikey = key
                    inv_info = {}
                    IsInvoice = True
                    inv_page_path = images_list[key]
                    inv_page_path_700 = images_list_700[key]

                    I_res_1 = predictInvoice(TFmodel2, inv_page_path)
                    # --print(I_res_1)
                    inv_info["isinvoice"] = I_res_1["I"]
                    inv_info["path"] = inv_page_path
                    if I_res_1["I"] >= 0.45:
                        json_path = inv_page_path.replace("png", "json")

                        with open(inv_page_path_700, "rb") as image_file:
                            base64_encoded_string = base64.b64encode(
                                image_file.read())

                            data_str = reader(inv_page_path)
                            feature_field = ['FORMS']
                            #################### PERFORMING OCR ##################

                            if mode == "notest":
                                # --print("fetching Boto Response .....")
                                response = client.analyze_document(
                                    Document={'Bytes': data_str}, FeatureTypes=feature_field)

                                with open(json_path, 'w') as fp:
                                    json.dump(response, fp)
                                json_files.append(json_path)

                            inv_info["json_path"] = json_path
                            # Extract key value pairs
                            raw_dict = get_raw_values(json_path)
                            print(raw_dict)

                            # raw_dict=get_raw_values(json_files[key])

                            field_list = {}
                            # converting k,v to nearest labels
                            for key, value in raw_dict.items():
                                check_res = field_match.get_field_label(
                                    key, w_list, match_threshold)
                                key = key.replace(":", "").replace(",", "")

                                if check_res[1] == None:
                                    data = {key.strip(): value[0].strip(
                                    ), "confidence_level": value[1]}
                                    field_list[key.strip()] = value[0].strip()

                                else:
                                    data = {check_res[1].strip(): value[0].strip(
                                    ), "confidence_level": value[1]}
                                    field_list[check_res[1].strip()
                                               ] = value[0].strip()

                            # field_list.append(data)
                            response2 = None
                            if not "Invoice_No" in field_list:
                                # searching invoice number

                                with open(inv_page_path_700, 'rb') as document:
                                    imageBytes = bytearray(document.read())

                                # Call Textract AnalyzeDocument by passing a document from local disk
                                response2 = textract.analyze_document(
                                    Document={'Bytes': imageBytes},
                                    FeatureTypes=["QUERIES"],
                                    QueriesConfig={
                                        "Queries": [{
                                            "Text": "What is invoice number?",
                                            "Alias": "Invoice_No"
                                        }]
                                    })

                                d = t2.TDocumentSchema().load(response2)
                                page = d.pages[0]

                                # get_query_answers returns a list of [query, alias, answer]
                                query_answers = d.get_query_answers(page=page)
                                if len(query_answers) > 0:
                                    if len(query_answers[0][2].strip()) > 0:
                                        field_list["Invoice_No"] = query_answers[0][2]
                                        print(
                                            "invoice number not found ,by query", query_answers[0][2])

                            # fetching form fields from
                            form_fields = []
                            table_dimensions = [0, 0]

                            # form_fields=getFormFieldsbyDistance(config_name,form_fields,json_path,table_dimensions)
                            # print("form fields :",form_fields)

                            # t1=Invoice_obj_list[0]['form_fields']
                            # t2=Invoice_obj_list[0]['fields']

                            for i in form_fields:
                                key = list(i.keys())[0]
                                if not (key in field_list):
                                    field_list[key] = i[key]

                            inv_info["fields"] = field_list
                    else:
                        IsInvoice = False

                    Invoice_obj_list[ikey] = inv_info

                    for key, value in Invoice_obj_list.items():
                        # print(key)
                        if key > 0:
                            I1 = Invoice_obj_list[key-1]
                            I2 = Invoice_obj_list[key]
                            if value["isinvoice"] < 0.45:  # i.e its not an invoice
                                cluster.append(key)
                            else:  # i.e document is invoice:
                                match_score = 0
                                for i in fields:
                                    match = 0
                                    absent_field = 0
                                    try:
                                        if (i in I1['fields']) and (i in I2['fields']):
                                            match = fuzz.ratio(I1['fields'][i].replace(
                                                'O', '0').strip(), I2['fields'][i].replace('O', '0').strip())
                                        elif not (i in I2['fields']):
                                            # i.e field is not present in secound Invoice
                                            absent_field = absent_field+1
                                    except:
                                        pass

                                    # print("match is ",match)
                                    match_score = match_score+match

                                # print("overall match score ",(match_score/len(fields)) )
                                if (match_score/len(fields)) > 94:
                                    # invoices are same
                                    cluster.append(key)
                                elif absent_field == len(fields):
                                    cluster.append(key)
                                else:
                                    cluster_list.append(cluster)
                                    cluster = []
                                    cluster.append(key)

                        else:
                            cluster.append(key)

                        if (len(cluster_list) > 1):
                            break

            if len(cluster) > 0:
                cluster_list.append(cluster)

        result = {"hasMultipleInvoice": len(cluster_list) > 1}

        if not mode == "test":  # "extraction_id" not in json_body.keys() :

            # Remove folder list
            for item in del_files:
                if path.exists(item):
                    os.remove(item)
            print("files cleaned...")

        #res = {"result": "done"}
        # res = {"result": "done"}
        update_status(request_id, 200)
        return HttpResponse(json.dumps(result, indent=4, sort_keys=True, default=str))
    def get(self, request):

        res = {"response": "ok"}
        return HttpResponse(res)


class InvoiceSplitV2(APIView):
    def __init__(self):
        super().__init__()
        textract_client = boto3.client('textract')
        self.is_invoice_thres = 0.20
        self.textract_document_reader = TextractDocumentTextDetector(
            textract_client)

    def predict_invoice_number(self, inv_text):
        with torch.no_grad():
            inp_ids = t5_tokenizer(inv_text + '</s>', return_tensors='pt',
                                   max_length=512, truncation=True, padding='max_length').input_ids
            outputs = inv_no_detector.generate(inp_ids)
            return t5_tokenizer.decode(outputs[0], skip_special_tokens=True)

    def post(self, request):
        del_files = []
        json_body = json.loads(request.body.decode("utf-8"))
        base64__ = json_body["data"]
        invoice_type = json_body["type"]
        mode = json_body["mode"]

        if "config_name" in json_body:
            config_name = json_body["config_name"]
        else:
            config_name = "global_config"

        if "return_type" in json_body:
            return_type = json_body["return_type"]
        else:
            return_type = "base64"

        if not checkconfignameexists(config_name.split('|')[0]):
            config_name = "global_config"

        request_id = log_to_db(request, 'InvoiceSplit', 'invoice_split_v2', config_name)
        config_name = config_name.split('|')[0].strip()

        unique_filename = None
        if mode == "test":
            if "extraction_id" in json_body:
                extraction_id = json_body["extraction_id"]
                unique_filename = extraction_id
            else:
                unique_filename = str(uuid.uuid4())

        else:
            unique_filename = str(uuid.uuid4())

        if invoice_type == "pdf":
            invoice_path = base_path+unique_filename+".pdf"
        else:
            error = {}
            error["error"] = "Not a pdf file"
            return HttpResponse(json.dumps(str(error), indent=4, sort_keys=True, default=str), status=500,)

        with open(os.path.expanduser(invoice_path), 'wb') as fout:
            fout.write(base64.b64decode(base64__))

        print("File Written "+invoice_path)
        del_files.append(invoice_path)

        images = convert_from_path(invoice_path, size=(2000, None))

        images_list = []
        images_count = 0
        for i in images:
            i.save(base_path+unique_filename+"_"+str(images_count)+".png")
            inv_img_path = base_path + unique_filename + \
                "_"+str(images_count)+".png"
            images_list.append(inv_img_path)
            del_files.append(inv_img_path)
            images_count = images_count+1
        print("images_list ", images_list)

        update_page_count(request_id, len(images))

        max_page_scan = get_maxpagescan()

        inv_obj_dict = {}
        for counter, inv_page_path in enumerate(images_list):
            if counter >= max_page_scan:
                break

            inv_info = {}
            I_res_1 = predictInvoice(TFmodel2, inv_page_path)
            print(f'Invoice predictor results: {I_res_1}')
            inv_info["isinvoice"] = I_res_1["I"]
            inv_info["path"] = inv_page_path

            if I_res_1["I"] >= self.is_invoice_thres:
                inv_text = self.textract_document_reader.detect_file_text(
                    inv_page_path)
                inv_info['invno'] = self.predict_invoice_number(inv_text)

                if mode == 'test':
                    inv_text_path = inv_page_path.replace("png", "txt")
                    with open(inv_text_path, 'w') as fp_img_text:
                        fp_img_text.write(inv_text)

            inv_obj_dict[counter] = inv_info

        cluster_list = []
        cluster = []
        for key, inv_obj in inv_obj_dict.items():
            print("*****************")
            print(key, inv_obj["isinvoice"], inv_obj)

            if key > 0:
                inv1 = None
                inv2 = None
                if "invno" in inv_obj_dict[key-1]:
                    inv1 = inv_obj_dict[key-1]
                elif key-2 in inv_obj_dict:
                    inv1 = inv_obj_dict[key-2]
                inv2 = inv_obj

                if inv1 is None:
                    cluster_list.append(cluster)
                    cluster = []
                    cluster.append(key)
                elif inv_obj["isinvoice"] < self.is_invoice_thres or inv_obj["invno"].lower() == "na":  # i.e its not an invoice
                    cluster.append(key)
                else:
                    inv1_no = inv1.get('invno', '').lower().strip()
                    inv2_no = inv2.get('invno', '').lower().strip()
                    if  inv2_no == inv1_no or inv2_no.lower() == 'none':
                        cluster.append(key)
                    else:
                        cluster_list.append(cluster)
                        cluster = []
                        cluster.append(key)
            else:
                cluster.append(key)

        if len(cluster) > 0:
            cluster_list.append(cluster)

        cl = []
        if (invoice_type == "pdf" and return_type == "pdf"):
            for ind, i in enumerate(cluster_list):
                d = {"invoiceid": ind, "invoicedata": self.getBase64OfPDF(
                    invoice_path, i), "pages": {"start": i[0]+1, "end": i[-1]+1}}
                cl.append(d)
        else:
            for i in cluster_list:
                l = []
                for index, val in enumerate(i):
                    if return_type == "str":
                        d = {"invoiceid": index, "invoicedata": images_list[val], "pages": {
                            "start": i[0]+1, "end": i[-1]+1}}
                    else:
                        with open(images_list[val], "rb") as image_file:
                            encoded_string = base64.b64encode(
                                image_file.read())
                            encoded_string = encoded_string.decode('utf-8')

                        d = {"invoiceid": index, "invoicedata": encoded_string,
                             "pages": {"start": i[0]+1, "end": i[-1]+1}}

                    l.append(d)
                cl.append(l)

        result = {"extraction_id": unique_filename, "invoices": cl}

        if not mode == "test":
            for item in del_files:
                if path.exists(item):
                    os.remove(item)
            print("files cleaned...")
        
        update_status(request_id, 200)

        return HttpResponse(json.dumps(result, indent=4, sort_keys=True, default=str))

    def get(self, request):
        res = {"response": "ok"}
        return HttpResponse(res)

    def get_base64(self, filepath):
        with open(filepath, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def getBase64OfPDF(self, input_pdf, pages):
        temp_pdf_filepath = input_pdf.replace(".pdf", "")+"_splited.pdf"
        input_pdf = Pdf.open(input_pdf)

        output = Pdf.new()
        for page in pages:
            output.pages.append(input_pdf.pages[page])
        output.save(temp_pdf_filepath)

        base64 = self.get_base64(temp_pdf_filepath)
        if os.path.exists(temp_pdf_filepath):
            os.remove(temp_pdf_filepath)
        return base64
