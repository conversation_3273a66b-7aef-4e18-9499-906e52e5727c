import glob

import os
import ntpath
import numpy as np
import cv2
import random
import itertools
import pandas as pd
from tqdm import tqdm
import urllib
import json
import PIL.Image as Image

from detectron2 import model_zoo
from detectron2.engine import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DefaultTrainer
from detectron2.config import get_cfg
from detectron2.utils.visualizer import Visualizer, ColorMode
from detectron2.data import DatasetCatalog, MetadataCatalog, build_detection_test_loader
from detectron2.evaluation import COCOEvaluator, inference_on_dataset
from detectron2.structures import BoxMode

#import seaborn as sns
from pylab import rcParams
import matplotlib.pyplot as plt
from matplotlib import rc
from fuzzywuzzy import fuzz
from AMS.settings import BASE_DIR
from AMS.extract_settings import *
import pickle
from Invoice.DbConfig import *



#########################Detectron loading model start ####################

py_path=None
if run_mode=='local':
    py_path='output/content/output/model_final.pth'

else:
    py_path='../output/content/output/model_final.pth'
    


cfg = get_cfg()
cfg.merge_from_file(model_zoo.get_config_file('COCO-Detection/faster_rcnn_R_50_FPN_3x.yaml'))
cfg.MODEL.ROI_HEADS.SCORE_THRESH_TEST = 0.3 # Set threshold for this model
cfg.MODEL.WEIGHTS = py_path # Set path model .pth
cfg.MODEL.ROI_HEADS.NUM_CLASSES = 3
cfg.MODEL.DEVICE="cpu"
predictor = DefaultPredictor(cfg)
print("pytorch model loaded ......")



################################checking overlap of cells and columns ############################

def overlap(rect1,rect2):
    x2=int(rect1[2])
    x1=int(rect1[0])

    xx2=int(rect2[2])
    xx1=int(rect2[0])
    
    if xx1 in range(x1-50,x2+50) and xx2 in range(x1-50,x2+50) :
      #its in the box
      return True
    else:
      return False


def overlapcells(rect1,rect2):
    #assuming rect2 smaller and rect1 larger 
    a1= (rect1[2]-rect1[0])*(rect1[3]-rect1[1])

    a2= (rect2[2]-rect2[0])*(rect2[3]-rect2[1])
    #print(a1,a2)
    
    #rect2 is greater than 
    if not a1>a2:
      temp=rect1
      rect1=rect2
      rect2=temp
      #print("rect 2 greater")
      
  
    
    #print("rect2 ",rect2)
    #print("rect1 ",rect1)
    
    x2=int(rect1[2])
    x1=int(rect1[0])
    y1=int(rect1[1])
    y2=int(rect1[3])

    xx2=int(rect2[2])
    xx1=int(rect2[0])
    yy1=int(rect2[1])
    yy2=int(rect2[3])
    
    if xx1 in range(x1,x2+10) and yy1 in range(y1,y2+10) :
      #its in the box
      return (True,a1,a2)
    else:
      return (False,a1,a2)  





###############################checking overlap of cells and columns ##############################


################################################getting content in the boxes :

def getTextFromBox(json_path,box,im_height,im_width):

    f = open(json_path,) 
    data = json.load(f)
    
    x1= box[0] -50
    x2= box[2] + 100
    y1= box[1] 
    y2= box[3]
    print("get text from box ",y1,x1,y2,x2)
    text=[]
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":

            BoundingBox=d["Geometry"]["BoundingBox"]
            add=d["Geometry"]["Polygon"]

             
            y1_= (add[1]["Y"]*im_height)
            y2_= (add[3]["Y"]*im_height)
            x1_= (add[0]["X"]*im_width)
            x2_= (add[2]["X"]*im_width)
            
            #print(d["Text"],"--",y1_,x1_,y2_,x2_)
            
            
            if y1_>y1 and y2_<y2 and x1_>x1 and x2_<x2:
                print("ex text ",d["Text"])
                #if len(d["Text"])>=3:
                text.append(d["Text"])
    print("text from box ",text)
    return text 



##################find horizontal lines by aws

def getrowrange(cell,finalrows,boxes):
    cx1,cy1,cx2,cy2,text=cell
    for row in finalrows:
        rx1,ry1,rx2,ry2=boxes[row].tolist()
        if int( ((cx1+cx2)/2) ) in range(int(rx1),int(rx2)):
            return row
    return None

def findlines_aws(json_path,y_min,y_max,width,height):
    f = open(json_path,) 
    data = json.load(f)


    #print("get text from box ",y1,x1,y2,x2)
    t_cells=[]
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":

            BoundingBox=d["Geometry"]["BoundingBox"]
            add=d["Geometry"]["Polygon"]

            
            y1_= (add[1]["Y"]*height)
            y2_= (add[3]["Y"]*height)
            x1_= (add[0]["X"]*width)
            x2_= (add[2]["X"]*width)

            #print( int(y1_) in range(int(y_min),int(y_max) ),y1_,y_min,y_max )

            if int(y1_) in range( int(y_min),int(y_max) ):
                #print(d['Text'])
                t_cells.append( [x1_,y1_,x2_,y2_,d['Text'] ] )
    return t_cells 


#####################Gets Rows if Table Detected 

def get_text(result, blocks_map):
    text = ''
    if 'Relationships' in result:
        for relationship in result['Relationships']:
            if relationship['Type'] == 'CHILD':
                for child_id in relationship['Ids']:
                    word = blocks_map[child_id]
                    if word['BlockType'] == 'WORD':
                        text += word['Text'] + ' '
                    if word['BlockType'] == 'SELECTION_ELEMENT':
                        if word['SelectionStatus'] =='SELECTED':
                            text +=  'X '    
    return text

def getlineitemsbyTables(json_path,config_name,y_min,y_max,width,height):
    min_col_in_line_items = 0
    res=Configuration.objects.filter(name=config_name)
    if res.exists():
        config=res.first()
        min_col_in_line_items=config.min_col_in_line_items   
    
    f = open(json_path,) 
    data = json.load(f)


    #print("get text from box ",y1,x1,y2,x2)
    t_cells=[]

    blocks=data['Blocks']
        

    blocks_map = {}
    table_blocks = []
    for block in blocks:
        blocks_map[block['Id']] = block
        if block['BlockType'] == "TABLE":
            table_blocks.append(block)

    table_data = []
    li_rows = {}
    for d in data["Blocks"]:
        #print(d["BlockType"])
        if d["BlockType"]=="TABLE":
            relationships=d["Relationships"]
            
            ids=relationships[0]['Ids']

            for child_id in ids:
                cell = blocks_map[child_id]
                if cell['BlockType'] == 'CELL':
                    BoundingBox=cell["Geometry"]["BoundingBox"]
                    add=cell["Geometry"]["Polygon"]

                    y1_= (add[1]["Y"]*height)
                    y2_= (add[3]["Y"]*height)
                    x1_= (add[0]["X"]*width)
                    x2_= (add[2]["X"]*width)



                    row_index = cell['RowIndex']
                    col_index = cell['ColumnIndex']
                    if int(y1_) in range( int(y_min)-30,int(y_max) ):
                        if row_index not in li_rows:
                            # create new row
                            li_rows[row_index] = {}
                        # get the text value
                        li_rows[row_index][col_index] = get_text(cell, blocks_map)
            table_data.append(li_rows)
            li_rows = {}

    ##16th March 2024
    ##FILTERING TABLE BASED ON THEIR COLUMN COUNT.
    ## IF COLUMN COUNT IS LESS THAN CONFIGURED COUNT THEN REMOVE THE TABLE FROM table_data.
    filtered_table_data = []
    for table in table_data:
        row_nos = table.keys()
        for row_id  in row_nos:
            if len(list(table[row_id].keys())) >= min_col_in_line_items:
                filtered_table_data.append(table)
                break

        

    data = {}
    index = 0
    for table in filtered_table_data:
        for elem in table.keys():
            index+=1
            data[index] = table[elem] 

    return dict(sorted(data.items()))


def getlineitemsbyTables_fallback(json_path):

    f = open(json_path,) 
    data = json.load(f)


    #print("get text from box ",y1,x1,y2,x2)
    t_cells=[]

    blocks=data['Blocks']
        

    blocks_map = {}
    table_blocks = []
    for block in blocks:
        blocks_map[block['Id']] = block
        if block['BlockType'] == "TABLE":
            table_blocks.append(block)

    li_rows = {}
    for d in data["Blocks"]:
        #print(d["BlockType"])
        if d["BlockType"]=="TABLE":
            relationships=d["Relationships"]
            
            ids=relationships[0]['Ids']

            for child_id in ids:
                cell = blocks_map[child_id]
                if cell['BlockType'] == 'CELL':
                    BoundingBox=cell["Geometry"]["BoundingBox"]
                    add=cell["Geometry"]["Polygon"]

                    


                    row_index = cell['RowIndex']
                    col_index = cell['ColumnIndex']
                    li_rows[row_index][col_index] = get_text(cell, blocks_map)
                  
                            
                        
                        

    return li_rows



def formcolscordsbycells_aws(t_cells,finalrows,boxes):

    final_t=[]
    cols_cords={}

    
    for cell2 in t_cells:
        #print(cell2)   
        c=cell2
        
        y=int(c[1])
        #y1=int(b[1])

        y_=int(c[3])
        #y1_=int(b[3])


        #print(y,y1,i)
        #if ( y1 in range(y-20,y+20) ) or y1_ in range(y_-20,y_+20) :
        #if len(cols_cords.keys())==0:
        #  cols_cords[(y,y_)]=[]
        keyfound=False
        row=getrowrange(cell2,finalrows,boxes)
        print('rows :',row)
        if row==None:
            continue
        for k in cols_cords.keys():
            
            if y in range(k[0]-20,k[1]+20) or y_ in range(k[0]-20,k[1]+20) :

                keyfound=True
                t=cols_cords[k]
                t.append( (cell2,row) )

                cols_cords[k]=t
            
        if keyfound==False:
            t=[]
            t.append( (cell2,row) )
            cols_cords[(y,y_)]=t
    return cols_cords

def getlineitemsbycells_aws(cols_cords):
    li_dict={}
    c=0
    for k,v in cols_cords.items():
        print(v)
        sorted_l=sorted(v,key=lambda x: x[1])
        t={}
        for i in sorted_l:
            if i[1] in t:
                ts=t[i[1]]  
                ts=ts+" "+i[0][4]
                t[i[1]]=ts
            else:
                t[i[1]]=i[0][4]
        
        li_dict[c]=t
        c=c+1

    return li_dict



header_items={"item name":'S',"sno":'S',"particular":'S',"description":'S',"rate":'M',"quantity":'S',"qty":'S',"total":'S',"amount":'M',"man day":'S',"hsn":'S',"price":'S',"uom":'S',"unit price":'M',"total":'S',"man days":'S',"grade":'S',"man month":'M',"sr.no":'S',"item":'S',"extended amount":'M',"net amount":'M',"part no":'S',"usage country":'S',"qty ordered":'S'}
header_items_list=list(header_items.keys())



def checkislineitemheader(row,df,header_list):
  
  
  
  finalrowcount=df.shape[1]
  match_count=0
  for col in row:
      
      for item in header_list:
          ratio=fuzz.partial_ratio(str(item).lower().strip(),str(col).lower().strip())
          #print("ratio >>>>",ratio,item,col)
          #ratio2=distance.get_jaro_distance(str(item).lower().strip(),str(v).lower().strip(), winkler=True, scaling=0.1)
          if ratio>80:
              match_count=match_count+1
              break
  lowerrange=0
  if finalrowcount>=5:
    lowerrange=4
  elif finalrowcount>=3:
    lowerrange=2
  else:
    lowerrange=finalrowcount
  
  print("matchcount ",match_count ," | lowerrange ",lowerrange," | finalrows ",finalrowcount)
  return match_count>=lowerrange
  #return match_count in range(lowerrange,finalrowcount ) 

#checks in data frame if header exists or not 
def checkheaderexists(df,finalrows,header_list):
    #df_=df
    print("checking header exists ")
   
    df_=df.transpose()
    print(df_)
    header_found=False
    header_index=0
    max_l=[]
    for i in range(0,df_.shape[0]):
        l=list(df_.iloc[i].values)
        
        res=checkislineitemheader( l ,df_ ,header_list)
        print("checking a row ",res)
        if res:
            
            max_l.append(i)
            #header_index=i
            #break
    
    print("matched header lists ",max_l)
    if len(max_l)>0:
        return (True,max(max_l))
    else:
        return (False,0)            
    #return (header_found,header_index)
 

#########################Detectron loading model end ####################




def predictpy(img_path):
    im = cv2.imread(img_path)
    print("image_path ", img_path)

    with torch.no_grad():  # Ensure no gradients are tracked
        outputs = predictor(im)

    outputs["instances"] = outputs["instances"].to("cpu")

    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    return outputs

def getlineitems(img_path, json_path, config_name):
    try:
        # Predict using PyTorch model
        outputs = predictpy(img_path)

        # Convert model outputs to plain Python objects early
        scores = list(outputs["instances"].scores)
        boxes = list(outputs["instances"].pred_boxes)
        classes = outputs["instances"].pred_classes

        rows, cells = [], []
        for index, cls in enumerate(classes):
            if cls.item() == 0:
                rows.append(index)
            elif cls.item() == 1:
                cells.append(index)

        finalrows = rows[:]
        for j in rows:
            rect2 = boxes[j].tolist()
            for i in rows:
                if i == j: continue
                rect1 = boxes[i].tolist()
                if overlap(rect1, rect2):
                    if rect1[3] > rect2[3] :
                        if j in finalrows:
                            finalrows.remove(j)
                    else:
                        if i in finalrows:
                            finalrows.remove(i)

        cell_d = {}
        for row in rows:
            r_x0, r_y0, r_x1, r_y1 = boxes[row].tolist()
            for cell in cells :
                c_x0, c_y0, c_x1,c_y1=boxes[cell].tolist()

                if c_x0>=(int(r_x0)-10) and c_x1<=(int(r_x1)+10):
                    if row in cell_d:
                        temp=cell_d[row]
                        temp.append(cell)
                    else:
                        temp=[]
                        temp.append(cell)
                        cell_d[row]=temp

        im = Image.open(img_path)
        width, height = im.size
        im.close()

        if not finalrows:
            return None

        y1_l = [boxes[i].tolist()[1] for i in finalrows]
        y2_l = [boxes[i].tolist()[3] for i in finalrows]
        y_min, y_max = min(y1_l), max(y2_l)

        li_rows = getlineitemsbyTables(json_path, config_name, y_min, y_max, width, height)
        df = pd.DataFrame(li_rows) if li_rows else None

        if df is not None:
            header_list = get_header_fields(config_name)
            header_exists, header_index = checkheaderexists(df, finalrows, header_list)
            if header_exists:
                df = df.transpose()
                header = df.iloc[header_index]
                new_df = pd.DataFrame(df.values[header_index+1:], columns=header)
                print("HEADER ROW:\n", header)
                print("SHAPE BEFORE:", df.shape)
                return new_df
    finally:
        # Explicit cleanup
        del outputs, scores, boxes, classes, rows, cells, finalrows, cell_d, df, li_rows
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

    return None

    #####Code Notes
    # Following code is finding lien items cells by geometry , 
    # aborting this code , traing AI for this     
    t_cells=findlines_aws(json_path,y_min,y_max,width,height)
    cols_cords=formcolscordsbycells_aws(t_cells,finalrows,boxes)
    li_dict=getlineitemsbycells_aws(cols_cords)
    df = pd.DataFrame(li_dict)
    
    
    header_exists,header_index=checkheaderexists(df,finalrows,header_list)
    print("header checks cells ",header_index,header_exists)
    df.fillna('', inplace=True)
    if header_exists:
        df=df.transpose()
        header=df.iloc[header_index]
        #df.iloc[header_index+1:]
        new_df  = pd.DataFrame(df.values[header_index+1:], columns=header)
        #new_df

        return new_df 
    else:
        df=df.transpose()
        header=df.iloc[0]
        #df.iloc[header_index+1:]
        new_df  = pd.DataFrame(df.values[1:], columns=header)
        #new_df

        return new_df




    
         


    
    

    ################Maling Line items Dictionry 

   

    
    
