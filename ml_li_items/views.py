from django.shortcuts import render
from AMS.settings import BASE_DIR

import numpy as np
import pandas as pd

from PIL import Image
import math
import os.path
from os import path
import base64

import webbrowser
import os
import json
import boto3
import io
from io import BytesIO
import sys
import os
import io
import magic
import PyPDF2
from AMS.extract_settings import *
from Invoice.ExtractUtils import *
from Invoice.Extract import *
from Invoice.LineSearch import *

from Invoice.ImageManipulation import *
from Invoice.FormFieldExtractionWrapper import *

from Invoice.Forms_Extraction import *
from Invoice.Vendor_Extraction import *
from Invoice.DbConfig import *
from Invoice.FieldMatch import * 

#import spacy
import uuid
import json
from django.http import HttpResponse
from pdf2image import convert_from_path, convert_from_bytes

from pdf2image.exceptions import (
    PDFInfoNotInstalledError,
    PDFPageCountError,
    PDFSyntaxError
)

# Create your views here.

from rest_framework.views import APIView
from Invoice.ML.TFPredictions import *

#New Line items 

from ml_li_items.mlextractLI.markheaders import *
from ml_li_items.mlextractLI.pytorchmodelload import * 
from Invoice.ResponseLayer import *
from custom_logger.custom_logger import CustomLogger
import trp.trp2 as t2
from Invoice.QueryConditions import *
from invoice_tracker.DbInvoiceTracker import *
from InvoiceSplit.utils import *
import time
class Version(APIView):
    def get(self, request):
        cl = CustomLogger("-")

        version = "0.1"
        try:
            file = open("../version.txt","r")
            version = file.readlines()[0]
            version = str(version)
        except:
            print("No file of version setting static version: 0.1")
        res = {"name":"InvoiceflowAI","response": "ok","version":version}
        cl.print("version is :"+str(version),request.user)

        return HttpResponse(json.dumps(res,indent=4, sort_keys=True, default=str))
""" 
print('base diretory '+BASE_DIR)
base_path = BASE_DIR+'/Invoice/pdf/'
# invoice_path=base_path+'invoice.png'
# image_path=invoice_path
# json_path=base_path+'invoice.json'
csv_path = base_path+'csv'
base_extract = BASE_DIR+'/extract/'
"""
# def getPageSize(invoice_path, amplify):
#     try:
#         page_sizes = []
#         with open(invoice_path, 'rb') as file:
#             pdf_reader = PyPDF2.PdfReader(file)

#             # Iterate through each page of the PDF
#             for page_num in range(len(pdf_reader.pages)):
#                 page = pdf_reader.pages[page_num]
#                 page_size = page.cropbox 
#                 page_sizes.append((page_size.upper_right[0]*amplify, page_size.upper_right[1]*amplify))

#     except:
#         page_sizes = []
#     return page_sizes

def getPageSize(invoice_path, amplify):
    try:
        page_sizes = []
        with open(invoice_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                box = page.cropbox  # or page.mediabox if you prefer full content
                # Calculate width and height from box coordinates
                width = (float(box.upper_right[0]) - float(box.lower_left[0])) * amplify
                height = (float(box.upper_right[1]) - float(box.lower_left[1])) * amplify
                print(f"Page {page_num+1}: width={width}, height={height}")
                page_sizes.append((width, height))
    except Exception as e:
        print("Error:", e)
        page_sizes = []
    return page_sizes
        

class NewInvoiceAPI(APIView):
    
    def reader(self, file):
        with open(file, "rb") as image_file:
            img_test = image_file.read()
            bytes_test = bytearray(img_test)
        return bytes_test

    def get(self, request):
        res = {"response": "ok","version":"0.1"}
        return HttpResponse(res)
    
    
    def post(self, request):
        request_start = time.time()
        start = time.time()
       
        #REMOTE_ADDR
        hostname=None
        headers="" #self.request.headers
        username=None
        agent=None
        scanned_pages=0
        try:
            hostname=self.request.META["HTTP_HOST"]
            pathinfo=self.request.META["PATH_INFO"]
            
            username=self.request.user.username
            agent= self.request.headers['User-Agent']

            
        except:
            pass
        
        
        
        result = {}
        config_name=None
        # Construction of JSON Structures
        unique_filename=None
        invoice_type=None
        config_name=None 

        #response layer object 
        response_layer=Responselayer()
        response_layer.vendor_name=None
        try:
            extracted_fields = {}
            form_fields = []

            json_body = json.loads(request.body.decode("utf-8"))
            base64__ = json_body["data"]
            mode = json_body["mode"]
            invoice_type = json_body["type"]
            #print(base64__)
            print("********")

            #Finding the configuration 
            if "config_name" in json_body:
                config_name=json_body["config_name"]
            else:
                config_name="global_config"   
            end = time.time()
            print("Time taken to read request body: ", end - start)

            #if config name does not exists then 
            start = time.time()
            if not checkconfignameexists(config_name.split('|')[0]):
                config_name="global_config" 
            end = time.time()
            print("Time taken to check config name: ", end - start)
            # 'api' is not folder name in this case as it's a misnomer. This api not just extracts
            # list items, but full invoice details.
            start=time.time()
            request_id = log_to_db(request, "invoice_extraction", 'new_invoice_api', config_name)
            config_name = config_name.split('|')[0].strip()
            end = time.time()
            print("Time taken to log to db: ", end - start)
            #checking if PDF FOLDER exists or not 
            #If PDF Folder does not exists , create it 

            if not os.path.isdir(BASE_DIR+'/Invoice/pdf'):
                os.mkdir(BASE_DIR+'/Invoice/pdf')
                print("pdf folder created...")
            
            run_mode=None

            if "extraction_id" in json_body.keys():
                unique_filename=json_body["extraction_id"]
                run_mode='Debug'
            
            else:
                unique_filename = str(uuid.uuid4())
                run_mode='Run'

            #db_logger.info("Invoice Path "+invoice_path,{"user": str(request.user) ,"entity":unique_filename} )
            cl = CustomLogger(unique_filename)
            invoice_type=invoice_type.strip().lower()
            #PATH where main invoice is stored 
            if invoice_type == "pdf":
                invoice_path = base_path+unique_filename+".pdf"
            else:
                invoice_path = base_path+unique_filename+".png"
            start = time.time()
            max_pagecount=get_max_pagecount(config_name) 
            end=time.time()
            print("Time taken to get max page count: ", end - start)
            
            
            try:

                #Saving the Invoice PDF/PNG
                start = time.time()
                with open(os.path.expanduser(invoice_path), 'wb') as fout:
                    fout.write(base64.b64decode(base64__))
                #print("File Written "+invoice_path)
                fout.close()
                end = time.time()
                print("Time taken to write file: ", end - start)
                ## trying to open generated images to validate 
                #images = convert_from_path(invoice_path, size=(2000, None))
                #images_700=convert_from_path(invoice_path,size=(772, 995))
                i = Image.open(invoice_path)
                i = None
            except :
                cl.print("Failed to create PNG file.. Trying to check file type from BASE64...")
                start = time.time()
                bytesData = io.BytesIO()
                bytesData.write(base64.b64decode(base64__))
                bytesData.seek(0)  # Jump to the beginning of the file-like interface to read all content!
                if("pdf" in (magic.from_buffer(bytesData.read()).lower())):
                    cl.print("Identified file type is PDF...")
                    if path.exists(invoice_path):
                        os.remove(invoice_path)
                    invoice_path = base_path+unique_filename+".pdf"
                    invoice_type = "pdf"
                    with open(os.path.expanduser(invoice_path), 'wb') as fout:
                        fout.write(base64.b64decode(base64__))
                    fout.close()
                end = time.time()
                print("Time taken to write file: ", end - start)

            images_list={}
            images_list_700={}
            json_list={}
            folder_list=[]
            mapping_list=[]
            invoice_checklist={}


            images_count=0
            #convert the pdf into image 
            if invoice_type=="pdf":

                # indentified the size of page
                start = time.time()
                pageSizes = getPageSize(invoice_path, 3)
                end = time.time()
                print("Time taken to get page size: ", end - start)
                # interate through config of each page and generate image
                start = time.time()
                if len(pageSizes) > 0:
                    images_700 = []
                    for i, size in enumerate(pageSizes):
                        images_700.append(convert_from_path(invoice_path, first_page=i+1, last_page=i+1, size=size)[0])
                else:
                    # images_700=convert_from_path(invoice_path,size=(772, 995))
                    images_700=convert_from_path(invoice_path,size=(2000, None))
                images = convert_from_path(invoice_path, size=(2000, None))
                end=time.time()
                print("Time taken to convert from path: ", end - start)
                #for im in images:
                #im.save(base_path+unique_filename+".png")
                
                start = time.time()
                for id,i in enumerate(images):
                    #break out if page count exceeds the dataabse configuration
                    if images_count >= max_pagecount:
                        cl.print("images count:"+str(images_count)+"max_pagecount :"+str(max_pagecount ))
                        break

                    i.save(base_path+unique_filename+"_"+str(id)+".png")
                    images_list[id]=base_path+unique_filename+"_"+str(id)+".png"
                    images_count=images_count+1

                for id,i in enumerate(images_700):
                    i.save(base_path+unique_filename+"_"+str(id)+"_700"+".png")
                    images_list_700[id]=base_path+unique_filename+"_"+str(id)+"_700"+".png"
                end = time.time()
                print("Time taken to save images: ", end - start)
            else:
                start = time.time()
                i = Image.open(invoice_path)
                i.save(base_path+unique_filename+"_"+str(images_count)+".png")
                images_list[images_count]=base_path+unique_filename+"_"+str(images_count)+".png"
                images_list_700[images_count]=base_path+unique_filename+"_"+str(images_count)+".png"
                images_count=images_count+1
                end = time.time()
                print("Time taken to save image: ", end - start)
                #print("exiting program....")
                #raise ValueError('A very specific bad thing happened.')

            print("images list ",images_list)
            print("images_list 700 ",images_list_700)
            image_keys=list(images_list.keys())
            image_keys.sort()
            start = time.time()
            update_page_count(request_id, images_count)
            end = time.time()
            print("Time taken to update page count: ", end - start)
            #Creating AMAZON Textract Instance 
            # Amazon Textract client
            textract = boto3.client('textract','us-east-1')
               
            start = time.time()
            # Instance of boto3 client 
            client = boto3.client( service_name='textract',
                region_name='us-east-1',endpoint_url='https://textract.us-east-1.amazonaws.com',aws_access_key_id="********************",
                        aws_secret_access_key="6629LxPd+So34S4EJPxsxs0KOvCwt/AwrjZkAHwN"
                ) 
            end = time.time()
            print("Time taken to create boto3 client: ", end - start)
            line_items_dict=[]
            lines=""
            flat_fields=None
            header_list=[]
            footer_list=[]

           
            AI_Fields={}
            Form_Present=False
            prediction_result={}
            #Iterating thorough all the images found  
            #res={"res":"ok"}
            #return HttpResponse(json.dumps(res,indent=4, sort_keys=True, default=str)) 
             
            
            

            db_extrtaction_default_list=get_extraction_list(config_name,'NearestDistance',hascondition_=False)
            db_extrtaction_list=get_extraction_list(config_name,'NearestDistance',hascondition_=True)

            invoice_list_info={}
            
            for key in image_keys:

                """
                if key>2:
                    break
                """
                inv_page_path=images_list[key]
                inv_page_path_700=images_list_700[key]
                if inv_page_path is not None :
                    start = time.time()
                    pred_res=predictInvoice(TFmodel2,inv_page_path)
                    end = time.time()
                    print("Time taken to predict invoice: ", end - start)
                    prediction_result[key]=pred_res
                    
                
                
            

                
               

                
                isInvoice=True
                if key>=0:
                    #check if it is invoice or not
                    
                    inv_page_path_1=None
                    I_res_1=None

                    if key+1 in images_list:
                        inv_page_path_1=images_list[key+1]

                        if inv_page_path_1 is not None:
                            if(images_count > 5):
                                start = time.time()
                                I_res_1=predictInvoice(TFmodel2,inv_page_path_1)
                                end = time.time()
                                print("Time taken to predict invoice: ", end - start)
                            else:
                                ##17/04/2024 HARDCODING PAGE AS INVOICE INCASE IF THE DOCUMENT IS <=5 PAGES.
                                I_res_1={"NI":0,"I":1}
                        cl.print("Invoice -Invoice Ml Prediction ")
                        cl.print(" ##################################### "+str(key+1)+" ################### "+str(I_res_1)) 

                         #check if NON-Invoice confidence is greater than equal to 70 
                        if I_res_1["NI"]>=0.80:
                            invoice_checklist[key+1]="NI"
                            invoice_checklist["last_invoice"]=key
                            
                        else:
                            invoice_checklist[key+1]="I"
                            
                            #print("Breaking ... out as page no : ",key," is Non Invoice" )
                            #break
                        print("invoice checklist -- ",invoice_checklist)
                        ####Invoice list is just for query execution hence increase the non invoice threshold to 70
                        #if I_res_1["NI"]>=0.70:
                        #    invoice_list_info[key]=[inv_page_path,"NI"]
                        #    isInvoice=False
                        #else:
                        #    invoice_list_info[key]=[inv_page_path,"I"]

                         

                    


                        #I_res=predictInvoice(TFmodel2,inv_page_path)
                    
                    



                    
                   
                         

                json_path = base_path+unique_filename+'_'+str(key)+'invoice.json'
                mapping_file=BASE_DIR+'/Invoice/pdf/'+unique_filename+'_'+str(key)+'mapping.json'
                csv_folder=BASE_DIR+'/Invoice/pdf/'+unique_filename+'_'+str(key)+"csv"
                base_extract = BASE_DIR+'/extract/'
                #db_logger.info("File Extracted Sucessfully",{"user": str(request.user) ,"entity":unique_filename} )
                
                #adding json lists :
                json_list[key]=json_path
                folder_list.append(json_path.replace('.json','-csv'))
                mapping_list.append(mapping_file)
              
                
                if key in invoice_checklist:
                    if invoice_checklist[key]=="NI":
                        isInvoice=False
                
                
                
                
                #Skew Correction 
                #skew logic is bugged in some cases 
                #skewImage(invoice_path)

                # invoice_file_=""
                # handling the case when its pdf

                # print("Invoice file ",invoice_file_)
                # invoice_path=base_extract+"/"+invoice_file_
 

                #Reading the Image 
                
                with open(inv_page_path_700, "rb") as image_file:
                    base64_encoded_string = base64.b64encode(image_file.read())

                data_str = self.reader(inv_page_path_700)
                #data_str = self.reader(inv_page_path)    
                
                # result["base64"]=base64_encoded_string
                # Perform OCR To invoice
                

                feature_field=""
                #Forms should only be fetched for First Invoice

                if key==0 or key==(len(images_list)-1) or key==(len(images_list)-2) :
                   
                    feature_field=['TABLES','FORMS']
                    Form_Present=True
                else:
                    if ("last_invoice" in invoice_checklist) and  (invoice_checklist["last_invoice"]==key):
                        feature_field=['TABLES','FORMS']
                        Form_Present=True
                    else:
                        feature_field=['TABLES']
                        Form_Present=False
                
                cl.print("Features Used "+str(feature_field))
                

                #################### PERFORMING OCR ##################
                if "extraction_id" not in json_body.keys() :
                    cl.print(message="fetching Boto Response .....")
                    start = time.time()
                    response = client.analyze_document(
                        Document={'Bytes': data_str}, FeatureTypes=feature_field)
                    with open(json_path, 'w') as fp:
                        json.dump(response, fp)
                    end = time.time()
                    print("Time taken to get boto response: ", end - start)
                    cl.print("JSON File Written ...")

                    scanned_pages+=1

                    #db_logger.info("OCR Successful",{"user": str(request.user) ,"entity":unique_filename} )

                #################### PERFORMING OCR ################## 

                

                #Extract AI Fields and merge into original fields 
                if Form_Present:
                    cl.print("getting values for "+str(json_path))
                    start = time.time()
                    temp_dict=get_raw_values(json_path)
                    end = time.time()
                    print("Time taken to get raw values: ", end - start)
                    #cl.print(str(key)+" temp_dict found "+str(temp_dict))
                    AI_Fields.update(temp_dict)


                # CREATING Objects
                table_util_obj = TableUtils()
                extract_util_obj = ExtractUtils()
                extract_obj = Extract()
                 

                #image_path = invoice_path #DupV
          
                #csv_path = base_path+unique_filename+'csv' #DupV

                # Loading Configuration File

                # Finding Dimensions of Image
                im = Image.open(inv_page_path)
                width, height = im.size

                # calling TbExtract - Table Extraction from JSON

                #csv_path = table_util_obj.parse_main(inv_page_path, json_path, height,mapping_file)
                
                ######################################################
                ############## PYTORCH MODEL EXRACTION ##############
                op_image=None
                df=None
                cl.print("marking for "+str(inv_page_path_700))
                try:
                    start = time.time()
                    op_image=markheader_main(json_path,inv_page_path_700,inv_page_path_700.replace(".png","-markup.png"),config_name )
                    end = time.time()
                    print("Time taken to mark header: ", end - start)
                except Exception as e:
                    cl.print("Marking Exception")
                    cl.print(str(e))
                
                #if op_image is None:
                #    continue
                
               
                
                if op_image is not None:
                    try:
                        start = time.time()
                        df=getlineitems(op_image,json_path,config_name)
                        end = time.time()
                        print("Time taken to get line items: ", end - start)
                        #cl.print("data frame lineitems ### ",len(df))
                        
                    except Exception as e:
                        #db_logger.info("Line Item Exception ### "+ str(e),{"user": str(request.user) ,"entity":unique_filename} )
                
                        cl.print("Line item Exception")
                        cl.print(str(e))

                        
                #print("output image ",op_image )
                #return HttpResponse(json.dumps(df.to_dict(),indent=4, sort_keys=True, default=str)) 
                # Extracting Header Items
                #response_data={"result":"ok"}
                #return HttpResponse(json.dumps(response_data,indent=4, sort_keys=True, default=str)) 
                ################################# temporary comments  ###################################
                """
                csv_file=None
                if csv_path is not None :
                    #Finds the Headers , Rename the Method Name
                    csv_file = extract_util_obj.findLineItemTable_V2(csv_path,config_name)
                    print("csv file name .....",csv_file) 

                if csv_file==None:
                    db_logger.info("No LineItems Detected",{"user": str(request.user) ,"entity":unique_filename} )
                    #raise ValueError('No Line Items Detected')
                
               
                #db_logger.info("Line Item csv file "+ str(csv_file),{"user": str(request.user) ,"entity":unique_filename} )
                  
                # Read the mapping file and get Dimensions of Table
                #check if csv file is not none 
                table_dimensions=[0,0]
                df=None
                if csv_file is not None :
                    mapping_data = ""
                    csv_file_path=BASE_DIR +'/Invoice/pdf/'+unique_filename+'_'+str(key)+'invoice-csv/'+csv_file
                    with open(mapping_file) as f:
                        mapping_data = json.load(f)
                    table_dimensions = mapping_data[csv_file_path]
                
                    table_confidence=table_dimensions[2]
                    print("table confidence ",table_confidence)


                    #line_items_list,top,bottom=extract_util_obj.findLineItems_V2(base_path+'invoice.json')
                    print("csv file_path ",csv_file_path)
                    #try:
                    df,top,bottom=extract_util_obj.findLineItems_V3( csv_file_path, json_path,config_name)
                    if df.empty:
                        df=extract_util_obj.findLineItemTable_raw(BASE_DIR +'/Invoice/pdf/'+unique_filename+'_'+str(key)+'invoice-csv/',config_name)
                    
                   
                    if  df is None:
                        df,top,bottom=extract_util_obj.findLineItems_bypatterns(json_path)
                        print(" finding line items by patterns ",df)
                        if top>0 and bottom>0:
                            table_dimensions=[top,bottom]
                    

                
                    #Cleaning Data Frame 
                    nan_value = float("NaN")  
                
                
                    if df is not None:
                        if not df.empty:
                            df.replace("", nan_value, inplace=True)
                            df=df.dropna(how='all',axis=1) 


                    #Break out of the Loop 
                    # If No Line Items are found in Page number
                    # Greater than 1

                    if key>0 and df is None:
                        print("No Line Items Found on page Number ",key)
                        print("\n Hence Breaking OUT....")  
                        break      

                    print("data frame *************")
                    print(df)
                    print("data frame**************")
                
                else:
                    #finding line items by patterns               
                    df,top,bottom=extract_util_obj.findLineItems_bypatterns(json_path)
                    print(" finding line items by patterns ",df)
                    if top>0 and bottom>0:
                        table_dimensions=[top,bottom]
                """        
                #check of csv file None type finishes here 

                table_dimensions=(0,0) 
                 # Finding Table Dimensions
                start = time.time()
                extract_util_obj.getHeight(inv_page_path)
                end = time.time()
                print("Time taken to get height: ", end - start)
                # extract_util_obj.getTableDimensions(json_path)
                # Finding Table Dimensions
                #extract_util_obj.getHeight(inv_page_path)
                # extract_util_obj.getTableDimensions(json_path)
                start = time.time()
                header_list = extract_util_obj.createText(
                json_path, extract_util_obj.height, extract_util_obj.width, table_dimensions[0], table_dimensions[1])
                end = time.time()
                print("Time taken to create text: ", end - start)
                start = time.time()
                footer_list = extract_util_obj.createTextFooter(
                json_path, extract_util_obj.height, extract_util_obj.width, table_dimensions[0], table_dimensions[1])
                end = time.time()
                print("Time taken to create footer text: ", end - start)
                ########### EXTRACTING FIELDS START ###########################

                #call formfields by Distance method 
                #Form Field will be only fetched for First Invocie 
               
                #if Form_Present:
                if isInvoice:    
                    start = time.time()
                    form_fields=getFormFieldsbyDistance(config_name,form_fields,json_path,db_extrtaction_default_list,table_dimensions,cl)
                    end = time.time()
                    print("Time taken to get form fields by distance(isInvoice): "+json_path+" :", end - start)
                    #call form fields search by linear method  
                    start = time.time()
                    form_fields,lines,flat_fields=getFormFieldsbyLinear(config_name,form_fields,json_path,table_dimensions,cl)
                    end=time.time()
                    print("Time taken to get form fields by linear: ", end - start)
                    
                ########### EXTRACTING FIELDS ENDS ###########################

                cl.print("extracted form fields ")
                cl.print(form_fields)
                start=time.time()
                confidence_dict=extract_util_obj.get_confidence_matrix(json_path)
                end = time.time()
                print("Time taken to get confidence matrix: ", end - start)
                #db_logger.info("data frame ### "+ str(df),{"user": str(request.user) ,"entity":unique_filename} )
                
                
                nan_value = float("NaN") 
                temp_list=[]
                if df is not None:
                    if not df.empty:
                        #Remove empty string from dataframe
                        start = time.time()
                        df=extract_util_obj.lineitemexclusions(config_name,df)
                        
                        df.replace(" ", nan_value, inplace=True)
                        df.replace("", nan_value, inplace=True)
                        df=df.dropna(how='all')
                        df=df.replace(nan_value, '', regex=True)
                        temp_list=extract_util_obj.df_to_dict(df,confidence_dict,config_name)
                        cl.print("Line Items added ",len(temp_list))
                        line_items_dict.append(temp_list) 
                        end=time.time()
                        print("Time taken to remove empty string: ", end - start)
                
                """ 
                else:
                    #data frame is None 
                    #Fetching alternate line items i
                    if not response_layer.executed:
                        response_layer.execute(images_list)

                    df=response_layer.lineitems 
                    temp_list=extract_util_obj.df_to_dict(df,confidence_dict,config_name)
                    line_items_dict.append(temp_list)    
                """


                
                if (key>0):
                    if invoice_checklist[key]=="NI":
                        cl.print(str(key)+ " is NI")
                        print("invoice checklist ")
                        print(invoice_checklist)
                        break
            

              
            
            #combile all the line items into one list 
            #for maintaining the structure 
            #return HttpResponse(json.dumps({},indent=4, sort_keys=True, default=str))  
            
            
            #extracted_fields["line_items"]=line_items_dict
            #extracted_fields["line_items"]=combined_line_items
            cl.print("line items dict "+str(line_items_dict))
            #removing empty lists if any 
            line_items_dict=[x for x in line_items_dict if x != []]
            
            
            if len(line_items_dict)==0:
                start = time.time()
                if not response_layer.executed:
                        response_layer.execute(images_list)
                end = time.time()
                print("Time taken to execute response layer: ", end - start)
                #print("li before ",images_list)
                #df=response_layer.lineitems 
                #line_items_dict=response_layer.structured_lineitems
                #temp_list=extract_util_obj.df_to_dict(df,confidence_dict,config_name)
                cl.print("structured line items "+str(response_layer.structured_lineitems))
                #db_logger.info("line items ### "+ str(response_layer.structured_lineitems),{"user": str(request.user) ,"entity":unique_filename} )
                
                line_items_dict.append(response_layer.structured_lineitems) 
          

          
            
            #line item counter
            combined_line_items=[]
            for i in line_items_dict:
                combined_line_items=combined_line_items+i


              ###### line item counter 
            li_counter=0
            for i in combined_line_items:
                i["fields"].append( {'id':li_counter} )
                li_counter=li_counter+1

            #extracted_fields["line_items"]=line_items_dict
            extracted_fields["line_items"]=combined_line_items

           
            ##earlier vendor logic placeholder

            
            #if extract_raw:
            #raw_dict=get_raw_values(json_list[0])
            raw_dict=AI_Fields
            temp_data={} 
            
            cl.print("AI Fields "+str(AI_Fields) )

            field_match=FieldMatch()
            start = time.time()
            db_extrtaction_list=get_extraction_list(config_name,None,True)
            end =time.time()
            print("Time taken to get extraction list: ", end - start)
            start = time.time()
            w_list=field_match.cerate_word_list(db_extrtaction_list)
            end = time.time()
            print("Time taken to create word list: ", end - start)
            start = time.time()
            match_threshold=get_ai_fieldmatch_threshold(config_name)
            end = time.time()
            print("Time taken to get ai field match threshold: ", end - start)
            cl.print("match threshold "+str(match_threshold))
           
            for key in list(raw_dict):
                value=raw_dict[key]
                if value[0].strip()=="":
                    raw_dict.pop(key)
            print("raw dict start")
            print(raw_dict)
            print("raw dict end")
            for key, value in raw_dict.items():
                    #print(key,' -- ',value)
                    #checking duplicate for extracted_field
                    #print("Match Field  checking ",key)
                    start = time.time()
                    
                    check_res=field_match.get_field_label(key,w_list,match_threshold) 
                    
                    key=key.replace(":","").replace(",","")
                    search_key=None
                    if check_res[1]==None:
                        data={key.strip():value[0].strip(),"confidence_level":value[1]}
                        search_key=key.strip()
                    else:
                        data={check_res[1].strip():value[0].strip(),"confidence_level":value[1]}
                        search_key=check_res[1].strip()
                    end = time.time()
                    print("Time taken to get field label: ", end - start)
                    #form_fields.append(data)
                    
                    #check for any duplicate present in the list 
                    start = time.time()
                    for d in form_fields:
                        l = [item.lower().strip() for item in list(d.keys())]
                        if search_key.lower().strip() in l:
                            cl.print("Duplicate found "+str(d) + " :for "+ str(check_res[1]) +" , Match_score :")
                            form_fields.remove(d)
                            
                          
                            #form_fields.append(d)
                            """
                            temp_val=d[check_res[1]]
                            d[check_res[1]+"_duplicate"]=temp_val
                            t=d
                            del d[check_res[1]]
                            form_fields.append(t)
                            """
                        
                    
                    form_fields.append(data)
                    end = time.time()
                    print("Time taken to check for duplicate: ", end - start)
                    #temp_data[key]=value
            
            print("checklist info ",prediction_result)
           
            ##############  Vendor Extraction Start ###############################
            start=time.time()
            companylist=get_companynames(config_name)
            end = time.time()
            print("Time taken to get company names: ", end - start)
            start = time.time()
            kv_vendorname,kv_score=filter_vendor_fields(form_fields,config_name,nlp) 
            end = time.time()
            print("Time taken to filter vendor fields: ", end - start)
            cl.print("Key Value Vendor name "+str(kv_vendorname))
            final_venname=None
            if kv_vendorname==None:
                json_path_v=json_list[0]
                start=time.time()
                data_str_v = self.reader(images_list[0])   
                response = client.detect_document_text(
                        Document={'Bytes': data_str_v})
                with open(json_path_v, 'w') as fp:
                    json.dump(response, fp)
                end = time.time()
                print("Time taken to write json file: ", end - start)
                print("jsn path vendor written ",json_path_v )
                start = time.time()
                ven_res=getVendors(TF_OB_model,images_list[0],json_path_v,companylist,config_name,response_layer,images_list)
                end =time.time()
                print("Time taken to get vendors: ", end - start)
                #vendor_match=getVendorbycsvMatch(header_list)
                cl.print("ven res "+str(ven_res))
                ven_alt=None

                if ven_res[0] is None or len(ven_res[0])==0:
                    if not response_layer.executed:
                        cl.print("images list vendor"+str(images_list))
                        ven_alt=response_layer.execute(images_list)
                    #ven_alt=response_layer.vendor_name
                    data={"vendor_name":ven_alt,"confidence_level":88 }
                    final_venname=ven_alt
                else:
                    data={"vendor_name":ven_res[0],"confidence_level":ven_res[1] }
                    final_venname=ven_res[0]


                
                if final_venname is not None:
                    form_fields.append(data)
            else:
                data={"vendor_name":kv_vendorname,"confidence_level":kv_score }
                final_venname=kv_vendorname
                form_fields.append(data)



            ############## Vendor Extraction End ###############################

            #Add query on vendorname
            """ 
            if final_venname is not None:
                querylist=getquerybyvendorname(config_name,final_venname)
                for key,doc in invoice_list_info.items():
                    if doc[1]=="I":
                        docpath=doc[0]
                        for query in querylist:
                            with open(docpath, 'rb') as document:
                                imageBytes = bytearray(document.read())

                            # Call Textract AnalyzeDocument by passing a document from local disk
                            response2 = textract.analyze_document(
                                Document={'Bytes': imageBytes},
                                FeatureTypes=["QUERIES"],
                                QueriesConfig={
                                    "Queries": [{
                                        "Text": query["query"],
                                        "Alias": query["fieldname"]
                                    }]
                                })

                            d = t2.TDocumentSchema().load(response2)
                            page = d.pages[0]

                            # get_query_answers returns a list of [query, alias, answer]
                            query_answers = d.get_query_answers(page=page) 
                            if len(query_answers)>0:
                                if len(query_answers[0][2].strip())>0:
                                    #field_list["Invoice_No"]=query_answers[0][2]
                                    data={query["fieldname"]:query_answers[0][2],"confidence_level":89.5 }
                                    form_fields.append(data)
                                    print(query["fieldname"]+" found ,by query ",query_answers[0][2])
                                    print(" query ",query["query"])
                                    print("fieldname ",query["fieldname"])
                                    print("query answers ",query_answers)
            """
            
            print("inv list ")
            print(images_list)
            print("inv list info")
            print(invoice_list_info)

            print("json")
            print(form_fields) 
            #conditional Form Fields
            
            for key in image_keys:
                inv_page_path=images_list[key]
                temp_arr=inv_page_path.replace(".png","invoice.json")
                #last paramter True for hascondition_=True
                if path.exists(temp_arr):
                    start = time.time()
                    form_fields=getFormFieldsbyDistance(config_name,form_fields,temp_arr,db_extrtaction_list,table_dimensions,cl,hascondition_=True)
                    end = time.time()
                    print("Time taken to get form fields by distance(path.exists): "+temp_arr+" : ", end - start)
                    #call form fields search by linear method  
                    start - time.time()
                    form_fields,lines,flat_fields=getFormFieldsbyLinear(config_name,form_fields,temp_arr,table_dimensions,cl,hascondition_=True)
                    end = time.time()
                    print("Time taken to get form fields by linear: ", end - start)





            #adding conditional queries START :
            
            print("prediction result  ",prediction_result)
            print( "images_list_700 ")
            print( images_list_700 )
            start = time.time()

            query_results=getQuery(config_name,form_fields,images_list_700,prediction_result) 
            end = time.time()
            print("Time taken to get query results: ", end - start)
            print("query results ",query_results)
            for k,v in query_results.items(): 
            
                data={k:v,"confidence_level":91.1 }
                form_fields.append(data)
            
             #adding conditional queries END :

           
                




                    





                    
                   
                    
            
                


                



            

            #Embedding Image Field 
            base64_data={"base64":base64__}

            #If Setting in Response 
            if image_in_response:
                extracted_fields["base64"]=base64__
            
            extracted_fields["extraction_id"]=unique_filename
            

            
            extracted_fields["uid"]=unique_filename
            extracted_fields["form_fields"]=form_fields
            extracted_data={}
            response_data={"extracted_data":extracted_fields}

            # print("extracted fields ",extract_fields)
            
            # Remove csv files extracted 
            


            if not mode=="test" : #"extraction_id" not in json_body.keys() :
                import shutil
                #removing folder
                
                start = time.time()
                #Remove folder list  
                for item in folder_list:
                    if path.exists(item):
                        shutil.rmtree(item)
                
                #removing all json files 
                for item in json_list:
                    if path.exists(json_list[item]):
                        os.remove(json_list[item] )
                
                #remove all the mapping files 
                for item in mapping_list:
                    if path.exists(item):
                        os.remove(item)
                
                for item in images_list:
                    if path.exists(images_list[item]):
                        os.remove(images_list[item] )
                
                
                for item in images_list_700:
                    if path.exists(images_list_700[item]):
                        os.remove(images_list_700[item] )


               
                

                if path.exists(invoice_path):
                    os.remove(invoice_path)
                end = time.time()
                print("Time taken to remove all files: ", end - start)
                cl.print("all files cleaned ......")
            
           
             
            
            

            response_layer.executed=False
            response_layer.response=None
            response_layer=None
            del response_layer

            #save data to invoice counter
             
            
        
            #invoice_tracker_save(config_name,"invoice","POST",headers,scanned_pages,images_count,hostname,invoice_path,username,agent)
            start = time.time()
            
            update_status(request_id, 200)
            end = time.time()
            print("Time taken to update status: ", end - start)
            print("Time taken to update status: ", end - request_start)
            
            return HttpResponse(json.dumps(response_data,indent=4, sort_keys=True, default=str)) 
        
        except ValueError as e:
            import traceback
            cl.print(traceback.format_exc())
            error={}
            error["error"]=str(e)
            cl.print("msg"+str(e))
            db_logger.exception("Line Item Exception",{"user": str(request.user) ,"entity":unique_filename})
            update_status(request_id, status=500, error_msg=str(e))
            return HttpResponse(json.dumps(error,indent=4, sort_keys=True, default=str))
        
        except Exception as e:
            import traceback
            cl.print(traceback.format_exc())
            error={}
            error["error"]="Invalid JSON/Corruption JSON values"
            cl.print("Error "+str(e))
            cl.print("inv type: ["+invoice_type+"] config name: ["+config_name+"]",request.user,True)
            cl.print("error "+ str(e)+ " invoicetype:"+invoice_type+", config_name:"+config_name,request.user,True,"exception")
            #db_logger.info(,{"user": str(request.user) ,"entity":unique_filename} )
            #db_logger.exception(, {"user": str(request.user) ,"entity":unique_filename})
            #db_logger.exception("Error",{"invoice_type": str(invoice_type) ,"config_name":config_name,"entity":unique_filename})
            update_status(request_id, status=500, error_msg=str(e))
            return HttpResponse(json.dumps(str(e),indent=4, sort_keys=True, default=str))
            
            